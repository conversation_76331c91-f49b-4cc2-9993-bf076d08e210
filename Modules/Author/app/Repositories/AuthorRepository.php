<?php

namespace Modules\Author\app\Repositories;

use Modules\Author\app\Models\Author;
use App\Repositories\Admin\Repository;

class AuthorRepository extends Repository
{
    /**
     * AuthorRepository constructor.
     * @param Author $model
     */
    public function __construct(Author $model)
    {
        $this->model = $model;
    }

    /**
     * Handle bulk actions for authors
     *
     * @param array $ids
     * @param string $status
     * @param string $actionType
     * @return array
     */
    public function bulkAction($ids, $status, $actionType): array
    {
        $type = 'success';
        switch ($actionType) {
            case 'delete':
                foreach ($ids as $id) {
                    $author = $this->model->findOrFail($id);

                    // Clear all media before deleting
                    $author->clearMediaCollection('profile_image');

                    $author->delete();
                }
                $message = __('author::author.author_deleted_successfully');
                break;
            case 'activate':
                // Get authors without images
                $authorsWithoutImages = $this->model->whereIn('id', $ids)
                    ->whereDoesntHave('media', function ($query) {
                        $query->where('collection_name', 'profile_image');
                    })
                    ->get(['id', 'name']);

                // Get authors with images (can be activated)
                $authorsWithImages = $this->model->whereIn('id', $ids)
                    ->whereHas('media', function ($query) {
                        $query->where('collection_name', 'profile_image');
                    })
                    ->pluck('id')
                    ->toArray();

                // Activate authors that have images
                if (!empty($authorsWithImages)) {
                    $this->model->whereIn('id', $authorsWithImages)->update(['status' => true]);
                }

                // Prepare response message
                if ($authorsWithoutImages->isNotEmpty()) {
                    if (!empty($authorsWithImages)) {
                        // Partial success - some activated, some couldn't be
                        $message = __('author::author.partial_activation_completed', [
                            'activated' => count($authorsWithImages),
                            'failed' => count($authorsWithoutImages)
                        ]);
                        $type = 'warning';
                    } else {
                        $message = __('author::author.cannot_activate_authors_without_images');
                        $type = 'error';
                    }
                } else {
                    // All authors activated successfully
                    $message = __('author::author.status_updated_successfully');
                    $type = 'success';
                }
                break;
            case 'deactivate':
                $this->model->whereIn('id', $ids)->update(['status' => false]);
                $message = __('author::author.status_updated_successfully');
                $type = 'success';
                break;

            default:
                $type = 'error';
                $message = __('author::author.something_wrong');
                break;
        }
        return [
            'type' => $type,
            'message' => $message
        ];
    }

    /**
     * Get authors ordered by display_order
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getActiveAuthors()
    {
        return $this->model->where('status', true)->orderBy('display_order', 'asc')->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get all authors
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAuthors()
    {
        return $this->model->orderBy('display_order', 'asc')->get();
    }

    /**
     * Find author by slug
     *
     * @param string $slug
     * @return Author|null
     */
    public function findBySlug($slug)
    {
        return $this->model->where('slug', $slug)->first();
    }

    /**
     * Get next display order
     *
     * @return int
     */
    public function getNextDisplayOrder()
    {
        $maxOrder = $this->model->max('display_order');
        return $maxOrder ? $maxOrder + 1 : 1;
    }

    /**
     * Get paginated active authors for frontend display
     *
     * @param int $perPage
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getPaginatedActiveAuthors($perPage = 15)
    {
        return $this->model->active()->orderBy('display_order', 'asc')->orderBy('created_at', 'desc')->paginate($perPage);
    }
}
