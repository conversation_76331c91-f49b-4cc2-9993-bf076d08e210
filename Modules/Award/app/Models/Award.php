<?php

namespace Modules\Award\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Translatable\HasTranslations;
use Modules\Author\app\Models\Author;

class Award extends Model
{
    use HasFactory, HasTranslations;

    /**
     * The attributes that are translatable.
     *
     * @var array
     */
    public $translatable = ['award_title', 'award_summary', 'award_from', 'award_description'];

    /**
     * The default locale to use for translations.
     *
     * @return string
     */
    public function getDefaultLocale(): string
    {
        return 'en';
    }

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'awards';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'author_id',
        'award_title',
        'award_summary',
        'award_from',
        'award_description',
        'award_year',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'award_year' => 'integer',
    ];

    /**
     * Relationship with Author model
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function author()
    {
        return $this->belongsTo(Author::class);
    }

    /**
     * Get the award icon URL.
     *
     * @return string
     */
    public function getIconUrl(): string
    {
        // Return a default icon URL for awards
        return asset('images/award-1.svg');
    }

    /**
     * Resolve route binding for encrypted IDs.
     *
     * @param mixed $value
     * @param string|null $field
     * @return self
     */
    public function resolveRouteBinding($value, $field = null): self
    {
        return $this->findOrFail(decrypt($value));
    }
}
