<?php

namespace Modules\Testimonial\app\Repositories;

use Modules\Testimonial\Models\Testimonial;
use App\Repositories\Admin\Repository;

class TestimonialRepository extends Repository
{
    /**
     * TestimonialRepository constructor.
     * @param Testimonial $model
     */
    public function __construct(Testimonial $model)
    {
        $this->model = $model;
    }

    /**
     * Handle bulk actions for testimonials
     *
     * @param array $ids
     * @param string $status
     * @param string $actionType
     * @return array
     */
    public function bulkAction($ids, $status, $actionType): array
    {
        $type = 'success';
        switch ($actionType) {
            case 'delete':
                foreach ($ids as $id) {
                    $testimonial = $this->model->findOrFail($id);

                    // Clear all media before deleting
                    $testimonial->clearMediaCollection('profile_image');

                    $testimonial->delete();
                }
                $message = __('testimonial::testimonial.testimonial_delete_successfully');
                break;
            case 'activate':
            case 'deactivate':
                $statusValue = $status == "Activate" ? true : false;
                $this->model->whereIn('id', $ids)->update(['status' => $statusValue]);
                $message = __('testimonial::testimonial.status_updated_successfully');
                break;

            default:
                $type = 'error';
                $message = __('testimonial::testimonial.something_wrong');
                break;
        }
        return [
            'type' => $type,
            'message' => $message
        ];
    }

    /**
     * Get active testimonials ordered by display order
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getActiveTestimonials()
    {
        return $this->model->active()->ordered()->get();
    }

    /**
     * Get testimonials for frontend display
     *
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getFrontendTestimonials($limit = null)
    {
        $query = $this->model->active()->ordered();

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * Get paginated testimonials for frontend display
     *
     * @param int $perPage
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getPaginatedFrontendTestimonials($perPage = 8)
    {
        return $this->model->active()->ordered()->paginate($perPage);
    }

    /**
     * Get next display order
     *
     * @return int
     */
    public function getNextDisplayOrder()
    {
        $maxOrder = $this->model->max('display_order');
        return $maxOrder ? $maxOrder + 1 : 1;
    }
}
