@extends('frontend.layouts.page')

@section('title', __('navigation.testimonials'))

@section('content')
    <!-- Testimonials Hero Section -->
    <section class="relative bg-center bg-cover h-[350px] sm:h-[400px]"
        style="background-image: url('images/testimonail-hero-section.webp');"
        aria-labelledby="testimonials-main-title">
        <!-- Dark overlay for better text readability -->
        <div class="absolute inset-0 bg-[linear-gradient(180deg,rgba(0,0,0,0.2)_20%,rgba(255,86,47,0.8)_172.67%)]">
        </div>

        <div class="relative z-10 container flex items-end h-full">
            <h1 id="testimonials-main-title" class="text-[2rem] lg:text-[3rem] text-white mb-12 relative z-10">
                {{ __('navigation.testimonials') }}
            </h1>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="container mx-auto py-6 md:py-10 lg:py-20" aria-labelledby="testimonial-heading">

        <!-- Testimonials Grid -->
        <div class="columns-1 md:columns-2 gap-6 lg:gap-8 space-y-6 lg:space-y-8">
            @foreach ($testimonials as $testimonial)
                <article
                    class="bg-white p-6 lg:p-8 border border-gray-100 break-inside-avoid hover:border-primary-500 transition-colors duration-200">
                    <blockquote class="mb-6">
                        <p class="text-gray-800 leading-[1.5] text-base font-bold lg:text-[1.125rem]">
                            {!! $testimonial->getTranslation('testimonial', app()->getLocale()) !!}
                        </p>
                    </blockquote>
                    <div class="flex items-center mb-4">
                        <img src="{{ $testimonial->getProfileImageUrl('thumbnail') }}"
                            alt="{{ $testimonial->getTranslation('author_name', app()->getLocale()) }}"
                            class="rounded-full object-cover aspect-square me-4" loading="lazy" width="48" height="48">
                        <div>
                            <h4 class="font-bold text-gray-800 text-lg">{{ $testimonial->getTranslation('author_name', app()->getLocale()) }}</h4>
                            <p class="text-gray-600 text-sm">{{ $testimonial->getTranslation('author_type', app()->getLocale()) }}</p>
                        </div>
                    </div>
                </article>
            @endforeach
        </div>

        <!-- Pagination -->
        {{ $testimonials->links('vendor.pagination.frontend') }}
    </section>
@endsection
