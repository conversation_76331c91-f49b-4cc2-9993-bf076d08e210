@extends('frontend.layouts.page')

@section('title', $author->getTranslation('name', app()->getLocale()))

@section('content')
    <!-- Author Profile Section -->
    <!-- Hero Section -->
    <section class="relative flex flex-col items-center justify-center bg-repeat bg-center py-[36px] lg:py-[48px]"
        style="background-image: asset('images/background.svg');" aria-label="Introduction to The Agency hero section">

        <!-- Top gradient overlay -->
        <div class="absolute top-0 left-0 w-full h-[60%] bg-gradient-to-b from-white to-transparent z-0"></div>

        <!-- Bottom gradient overlay -->
        <div class="absolute bottom-0 left-0 w-full h-[60%] bg-gradient-to-t from-white to-transparent z-0"></div>

        <!-- Content wrapper -->
        <div class="relative z-10 container">
            <!-- Back Button -->
            <div class="mb-6 group">
                <a href="{{ route('authors.index') }}" aria-label="Back to Author List page"
                    class="inline-flex items-center text-gray-900 group-hover:text-primary-500 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                    <span class="group-hover:text-primary-500 duration-200 transition-colors icon-arrow-left me-2"></span>
                    <span class="uppercase">back</span>
                </a>
            </div>
            <!-- Author Profile Content -->
            <div class="bg-gradient-to-b from-[#FFF2ED] from-50% to-transparent to-50% text-center">
                <h1
                    class="font-sans-normal font-regular leading-[1.4] text-[1.75rem] sm:text-[2rem] lg:text-[2.5rem] text-gray-800 mb-4">
                    {{ $author->getTranslation('name', app()->getLocale()) }}
                </h1>

                <!-- Author Image -->
                <div class="relative inline-block">
                    <div class="relative w-[301px] h-[466px] mx-auto overflow-hidden aspect-ratio[1/1.5]">
                        <!-- Background overlay with 50% opacity -->

                        <img src="{{ $author->getProfileImageUrl('preview') }}"
                            alt="{{ $author->getTranslation('name', app()->getLocale()) }}, acclaimed fiction author and literary figure"
                            class="w-full h-full object-cover" loading="lazy">
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="container">
        <div class="content-section">
            <div id="biography-text" class="line-clamp-5">
                {!! $author->getTranslation('bio', app()->getLocale()) !!}
            </div>

            <!-- Toggle button -->
            <button id="toggle-btn"
                class="flex justify-end mt-1 ms-auto btn btn-primary btn-line-effect text-sm capitalize font-normal"
                onclick="toggleContent()" aria-expanded="false" aria-label="Toggle biography content">
                Show More
            </button>
            </div>
    </section>
    <section class="py-6 md:py-10 lg:py-20 relative">

        <div class="container relative z-10">
            <h2 class="mb-4 md:mb-8 xl:mb-12 leading-[1.1]">Books</h2>
            <!-- Books Grid -->
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6 lg:gap-8">
                @forelse($books as $book)
                    <div class="author-card" role="listitem">
                        <a href="{{ route('books.show', $book->slug) }}" class="group block focus:outline-0 focus:ring-0"
                            aria-label="View details for '{{ $book->getTranslation('title', app()->getLocale()) }}' book">
                            <article>
                                <div class="author-image mb-0">
                                    <img src="{{ $book->getCoverImageUrl('preview') }}"
                                        alt="Book cover for '{{ $book->getTranslation('title', app()->getLocale()) }}'"
                                        loading="lazy">
                                </div>
                            </article>
                        </a>
                    </div>
                @empty
                    <div class="col-span-full text-center py-8">
                        <p class="text-gray-500">No books available for this author.</p>
                    </div>
                @endforelse
            </div>
            @if($books->count() > 10)
                <div class="flex justify-center mt-8 sm:mt-12">
                    <a href="{{ route('books.index') }}" class="btn btn-primary btn-line-effect">View All</a>
                </div>
            @endif
        </div>
    </section>
    <!-- Awards & Recognition Section -->
    <section class="pb-6 md:pb-10 lg:pb-20" aria-labelledby="awards-heading">
        <div class="container">
            <h2 id="awards-heading" class="sr-only">Awards and Recognition</h2>
            <!-- Tab Navigation -->
            <div class="flex flex-nowrap justify-start mb-8 gap-4" role="tablist" aria-labelledby="awards-heading">
                <button id="awards-tab"
                    class="custom-tab active py-3 text-base font-semibold border-b-2 border-primary-500 text-primary-500 hover:text-primary-600 focus:outline-none focus:border-b-2 focus:border-primary-600"
                    role="tab" aria-selected="true" aria-controls="awards-panel">
                    Awards & Honors
                </button>
                <button id="acclaim-tab"
                    class="custom-tab py-3 text-base font-semibold border-b-2 border-transparent text-gray-900 hover:text-primary-500 hover:border-primary-500 focus:outline-none focus:border-b-2 focus:border-primary-600"
                    role="tab" aria-selected="false" aria-controls="acclaim-panel">
                    Critical Acclaim in the Arab
                </button>
            </div>

            <!-- Awards Panels -->
            <!-- Awards & Honors Panel -->
            <div id="awards-panel" class="custom-tab-panel" role="tabpanel" aria-labelledby="awards-tab">
                <div class="columns-1 md:columns-2 gap-6 lg:gap-8 space-y-6 lg:space-y-8">
                    @forelse($awards as $award)
                        <!-- Award Item -->
                        <article
                            class="bg-white border p-6 border-gray-100 break-inside-avoid hover:border-primary-500 transition-colors duration-200">
                            <div class="flex items-center gap-4">
                                <!-- Award Content -->
                                <div class="flex-1">
                                    <h4 class="text-gray-800 text-lg font-bold">{{ $award->getTranslation('award_title', app()->getLocale()) }}</h4>
                                    <p class="text-gray-900 font-normal text-base">{{ $award->award_year }}</p>
                                </div>
                            </div>
                        </article>
                    @empty
                        <div class="col-span-full text-center py-8">
                            <p class="text-gray-500">No awards available for this author.</p>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- Critical Acclaim Panel -->
            <div id="acclaim-panel" class="custom-tab-panel hidden acclaim-panel" role="tabpanel"
                aria-labelledby="acclaim-tab">
                <div class="columns-1 gap-6 lg:gap-8 space-y-6 lg:space-y-8">
                    @forelse($criticalAcclaims as $index => $acclaim)
                        <!-- acclaim Item -->
                        <article
                            class="bg-white p-6 lg:p-8 border border-gray-100 hover:border-primary-500 transition-colors duration-200">
                            <button
                                class="custom-collapse w-full text-left flex justify-between items-center focus:outline-none"
                                aria-expanded="false" data-target="acclaim-{{ $index + 1 }}">
                                <span
                                    class="question-text text-gray-800 leading-[1.5] text-base font-bold lg:text-[1.125rem] pe-4 transition-colors duration-200">{{ $acclaim->getTranslation('heading', app()->getLocale()) }}</span>
                                <span
                                    class="faq-icon icon-add-circle text-xl text-gray-500 transition-transform duration-200 flex-shrink-0"></span>
                            </button>
                            <div id="acclaim-{{ $index + 1 }}" class="hidden mt-4">
                                {!! $acclaim->getTranslation('summary', app()->getLocale()) !!}
                            </div>
                        </article>
                    @empty
                        <div class="col-span-full text-center py-8">
                            <p class="text-gray-500">No critical acclaim available for this author.</p>
                        </div>
                    @endforelse
                </div>
            </div>
            <div class="flex justify-center mt-8 sm:mt-12">
                <a href="#" class="btn btn-primary btn-line-effect">View All</a>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
<script>
    function toggleContent() {
        const content = document.getElementById('biography-text');
        const button = document.getElementById('toggle-btn');

        if (content.classList.contains('line-clamp-5')) {
            // Show full content
            content.classList.remove('line-clamp-5');
            button.textContent = 'Show Less';
            button.setAttribute('aria-expanded', 'true');
            // Announce to screen readers
            if (window.SLA && window.SLA.announceToScreenReader) {
                window.SLA.announceToScreenReader('Biography expanded');
            }
        } else {
            // Show only 5 lines
            content.classList.add('line-clamp-5');
            button.textContent = 'Show More';
            button.setAttribute('aria-expanded', 'false');
            // Announce to screen readers
            if (window.SLA && window.SLA.announceToScreenReader) {
                window.SLA.announceToScreenReader('Biography collapsed');
            }
        }
    }
</script>
@endpush
