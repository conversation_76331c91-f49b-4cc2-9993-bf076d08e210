@extends('frontend.layouts.page')

@section('title', __('navigation.authors'))

@section('content')
    <!-- Featured Authors Hero Section -->
    <section class="relative h-[350px] sm:h-[400px] bg-primary-100"
        style="background-image: url('images/new-pattern.svg');" aria-labelledby="authors-main-title">

        <div class="absolute bottom-0 left-0 w-full h-[5rem] bg-gradient-to-t from-white to-transparent z-0"></div>
        <div class="relative z-10 container flex items-end h-full">
            <h1 id="authors-main-title" class="text-[2rem] lg:text-[3rem] text-gray-800 mb-12 relative z-10">
                {{ __('navigation.authors') }}
            </h1>
        </div>
    </section>

    <!-- Featured Authors Grid Section -->
    <section class="relative pt-6 lg:pt-8 md:pb-10 lg:pb-20 bg-primary-100/50" aria-labelledby="featured-authors-heading">
        <div class="absolute top-0 left-0 w-full h-full opacity-25"
            style="background-image: url('images/section-pattern.svg');"></div>
        <!-- Top gradient overlay -->
        <div class="absolute top-0 left-0 w-full h-[40%] bg-gradient-to-b from-white to-transparent z-0"></div>

        <!-- Bottom gradient overlay -->
        <div class="absolute bottom-0 left-0 w-full h-[40%] bg-gradient-to-t from-white to-transparent z-0"></div>
        <div class="relative z-10 container mx-auto">
            <h2 id="featured-authors-heading" class="sr-only">Our Featured Authors</h2>
            <!-- Authors Grid -->
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6 lg:gap-8">
                @foreach ($authors as $author)
                    <div class="author-card" role="listitem">
                        <a href="{{ route('authors.show', $author->slug ?? $author->id) }}" class="group block focus:outline-0 focus:ring-0"
                            aria-label="View {{ $author->getTranslation('name', app()->getLocale()) }}'s profile and works">
                            <article>
                                <div class="author-image">
                                    <img src="{{ $author->getProfileImageUrl('preview') }}"
                                        alt="Professional photo of {{ $author->getTranslation('name', app()->getLocale()) }}"
                                        loading="lazy">
                                </div>
                                <h5
                                    class="group-hover:text-primary-500 group-focus:text-primary-500 transition-colors duration-200 text-center">
                                    {{ $author->getTranslation('name', app()->getLocale()) }}
                                </h5>
                            </article>
                        </a>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            {{ $authors->links('vendor.pagination.frontend') }}
        </div>
    </section>

@endsection
