@extends('frontend.layouts.page')

@section('title', __('navigation.news'))

@section('content')
    <!-- News Hero Section -->
    <section class="relative bg-center bg-cover h-[350px] sm:h-[400px]"
        style="background-image: url('images/news-hero-section.webp');" aria-labelledby="news-main-title">
        <!-- Dark overlay for better text readability -->
        <div class="absolute inset-0 bg-[linear-gradient(180deg,rgba(0,0,0,0.2)_20%,rgba(255,86,47,0.8)_172.67%)]">
        </div>

        <div class="relative z-10 container flex items-end h-full">
            <h1 id="news-main-title" class="text-[2rem] lg:text-[3rem] text-white mb-12 relative z-10">
                {{ __('navigation.news') }}
            </h1>
        </div>
    </section>

    <!-- Latest News Section -->
    <section class="container mx-auto py-6 md:py-10 lg:py-20" aria-labelledby="latest-news-heading">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 xl:gap-y-12 mb-12">
            @foreach ($news as $item)
                <article class="group flex flex-col h-full" aria-labelledby="article-{{ $item->id }}-title">
                    <div class="space-y-4 flex flex-col flex-1">
                        <div class="aspect-[4/3] overflow-hidden relative">
                            <img src="{{ $item->getFeaturedImageUrl('medium') }}" alt="{{ $item->title }}"
                                class="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
                                loading="lazy">
                            <span
                                class="absolute top-4 left-4 bg-white px-4 py-2.5 text-primary-500 text-sm font-semibold rounded-full">
                                MANUSCRIPT
                            </span>
                        </div>
                        <div class="flex flex-col flex-1">
                            <!-- Author and date -->
                            <div class="flex items-center gap-2 mb-3 flex-wrap">
                                <div class="flex items-center gap-2">
                                    <img src="{{ $item->author->getProfileImageUrl('thumbnail') }}"
                                        alt="{{ $item->author?->name ?? 'Author' }} profile photo"
                                        class="w-7 h-7 rounded-full object-cover aspect-square" loading="lazy">
                                    <span>{{ $item->author?->name ?? 'Author' }}</span>
                                </div>
                                <time datetime="{{ $item->created_at->format('Y-m-d') }}" class="flex items-center ms-auto">
                                    <span class="icon-calendar text-[16px] me-[0.5rem]"></span>
                                    {{ $item->created_at->format('M d, Y') }}
                                </time>
                            </div>
                            <h5 id="article-{{ $item->id }}-title"
                                class="font-bold text-gray-800 mb-2 line-clamp-2 transition-colors duration-200 group-hover:text-primary-500">
                                {{ $item->title }}
                            </h5>
                            <p class="text-gray-900 line-clamp-2 mb-4">
                                {{ Str::limit(strip_tags($item->content), 120) }}
                            </p>
                            <a href="{{ route('news.show', $item->slug) }}"
                                class="btn btn-primary btn-line-effect uppercase mt-auto self-start w-auto"
                                aria-label="Read more about {{ $item->title }}">
                                READ MORE
                            </a>
                        </div>
                    </div>
                </article>
            @endforeach
        </div>

        <!-- Pagination -->
        {{ $news->links('vendor.pagination.frontend') }}
    </section>
@endsection
