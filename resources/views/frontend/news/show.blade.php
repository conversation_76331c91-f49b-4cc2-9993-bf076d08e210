@extends('frontend.layouts.page')

@section('title', $article->getTranslation('title', app()->getLocale()))

@section('content')
    <!-- Article Detail Section -->
    <section class="container mx-auto py-6 md:pb-10 lg:pb-20">
        <div class="max-w-4xl mx-auto">
            <!-- Back Button -->
            <div class="mb-6 group">
                <a href="{{ route('news.index') }}" aria-label="Back to News page"
                    class="inline-flex items-center text-gray-900 group-hover:text-primary-500 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                    <span class="group-hover:text-primary-500 duration-200 transition-colors icon-arrow-left me-2"></span>
                    <span class="uppercase">back</span>
                </a>
            </div>

            <!-- Article Header -->
            <article>
                <header class="mb-6">
                    <h1 class="heading mb-6">
                        {{ $article->title }}
                    </h1>

                    <!-- Author and Date -->
                    <div class="flex items-center gap-3 mb-4">
                        <img src="{{ $article->author->getProfileImageUrl('thumbnail') }}"
                            alt="{{ $article->author?->name ?? 'Author' }} profile photo" class="w-7 h-7 rounded-full"
                            loading="lazy">
                        <div class="flex items-center gap-4">
                            <span class="text-gray-800 font-medium">{{ $article->author?->name ?? 'Author' }}</span>
                            <time datetime="{{ $article->created_at->format('Y-m-d') }}"
                                class="flex items-center text-gray-600">
                                <span
                                    class="icon-calendar text-[16px] me-2"></span>{{ $article->created_at->format('M d, Y') }}
                            </time>
                        </div>
                    </div>

                    <!-- Category Tag -->
                    <div class="mb-6 lg:mb-12">
                        <span
                            class="inline-block text-gray-900 text-sm font-semibold px-4 py-2 rounded-full border border-gray-100">
                            MANUSCRIPT
                        </span>
                    </div>
                </header>

                <!-- Article Image -->
                <div class="mb-8 relative aspect-[16/9] overflow-hidden">
                    <div class="relative">
                        <img src="{{ $article->getFeaturedImageUrl('main') }}" alt="{{ $article->title }}"
                            class="w-full h-auto object-cover" loading="lazy">
                    </div>
                    <div
                        class="absolute inset-0 bg-[linear-gradient(180deg,rgba(0,0,0,0.2)_20%,rgba(255,86,47,0.8)_172.67%)]">
                    </div>
                </div>

                <!-- Article Content -->
                <div class="content-section">
                    {!! $article->content !!}
                </div>

                <!-- Share Section -->
                <div class="pt-8">
                    <h4 class="mb-4">Share</h4>
                    <div class="flex items-center gap-4">
                        <a href="#" rel="noopener nofollow"
                            class="w-12 h-12 group rounded-full border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-primary-500 hover:text-white hover:border-primary-500 transition-all duration-200"
                            aria-label="Share on Instagram">
                            <span class="icon-instagram text-xl group-hover:text-white"></span>
                        </a>
                        <a href="#" rel="noopener nofollow"
                            class="w-12 h-12 group rounded-full border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-primary-500 hover:text-white hover:border-primary-500 transition-all duration-200"
                            aria-label="Share on Facebook">
                            <span class="icon-facebook text-xl group-hover:text-white"></span>
                        </a>
                        <a href="#" rel="noopener nofollow"
                            class="w-12 h-12 group rounded-full border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-primary-500 hover:text-white hover:border-primary-500 transition-all duration-200"
                            aria-label="Share on LinkedIn">
                            <span class="icon-linkedin text-xl group-hover:text-white"></span>
                        </a>
                    </div>
                </div>
            </article>
        </div>
    </section>
@endsection
