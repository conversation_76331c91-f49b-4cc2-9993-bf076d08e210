<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Modules\EmailTemplate\app\Models\EmailTemplate;

class UserVerificationLink extends Notification
{
    /**
     * Create a new notification instance.
     *
     * @return void
     */
    protected $params;
    public $emailTemplate;
    public $mailData;

    public function __construct($params)
    {
        $this->params = $params;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $this->emailTemplate = new EmailTemplate();

        $emailTemplateNew = $this->emailTemplate->getTemplateToSend(['key' => 'REGISTER_USER_BY_ADMIN']);

        $this->mailData['NAME'] = ucfirst($notifiable->first_name);
        $this->mailData['PASSWORD'] = $this->params['data']['password'];
        $this->mailData['USER'] = $notifiable->email;

        foreach ($this->mailData as $key => $value) {
            $emailTemplateNew->email_content = str_replace($key, $value, $emailTemplateNew->email_content);
        }

        return (new MailMessage)
            ->subject($emailTemplateNew->subject)
            ->markdown('emails.common_mail', ['html' => $emailTemplateNew->email_content]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
