<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Modules\Author\app\Repositories\AuthorRepository;
use Modules\Book\app\Repositories\BookRepository;
use Modules\Award\app\Repositories\AwardRepository;
use Modules\CriticalAcclaim\app\Repositories\CriticalAcclaimRepository;

class AuthorController extends Controller
{
    protected $authorRepository;
    protected $bookRepository;
    protected $awardRepository;
    protected $criticalAcclaimRepository;

    public function __construct(
        AuthorRepository $authorRepository,
        BookRepository $bookRepository,
        AwardRepository $awardRepository,
        CriticalAcclaimRepository $criticalAcclaimRepository
    ) {
        $this->authorRepository = $authorRepository;
        $this->bookRepository = $bookRepository;
        $this->awardRepository = $awardRepository;
        $this->criticalAcclaimRepository = $criticalAcclaimRepository;
    }
    /**
     * Display all authors with pagination and filtering.
     */
    public function index(): View
    {
        $authors = $this->authorRepository->getPaginatedActiveAuthors(15);
        return view('frontend.authors.index', compact('authors'));
    }

    /**
     * Display a specific author profile.
     */
    public function show(string $authorSlug): View
    {
        // Get author with their books and awards
        $author = $this->authorRepository->findBySlug($authorSlug);

        if (!$author || !$author->status) {
            abort(404);
        }

        // Get author's books
        $books = $this->bookRepository->getBooksWithAuthor()
            ->where('author_id', $author->id)
            ->where('status', 'published')
            ->orderBy('year_published', 'desc')
            ->get();

        // Get author's awards
        $awards = $this->awardRepository->getAwardsByAuthor($author->id);

        // Get critical acclaims for this author
        $criticalAcclaims = $this->criticalAcclaimRepository->getWithAuthor()
            ->where('author_id', $author->id)
            ->orderBy('id', 'asc')
            ->get();

        return view('frontend.authors.show', [
            'author' => $author,
            'books' => $books,
            'awards' => $awards,
            'criticalAcclaims' => $criticalAcclaims,
        ]);
    }
}
