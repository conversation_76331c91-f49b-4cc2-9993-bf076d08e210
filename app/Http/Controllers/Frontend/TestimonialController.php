<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\View\View;
use Modules\Testimonial\app\Repositories\TestimonialRepository;

class TestimonialController extends Controller
{
    protected $testimonialRepository;

    public function __construct(
        TestimonialRepository $testimonialRepository
    ) {
        $this->testimonialRepository = $testimonialRepository;
    }
    /**
     * Display all testimonials.
     */
    public function index(): View
    {
        $testimonials = $this->testimonialRepository->getPaginatedFrontendTestimonials(8);
        return view('frontend.testimonials.index', compact('testimonials'));
    }
}
